part of 'reader_base.dart';

class Reader<PERSON><PERSON>yon<PERSON> extends ReaderBase {
  ReaderTiyong();

  @override
  String get feature => '''
## 體用生剋解讀
### 內容：
將卦分為「體卦」與「用卦」，並以五行生剋來分析二者間的關係，進而判斷事件發展。

### 術語：
體：代表自身或主體。
用：代表對方、外在事物。

### 目的：
分析內外力量互動，研判變化趨勢。

### 特色：
- 技術性高，常見於高階命理分析。
- 常與納甲、五行等系統結合使用。
''';

  @override
  (String, String) getPrompt(YijingModel model) {
    String sysPrompt = """
你是梅花易數專家，用簡單的話解讀卦象。直接說結果就好，不要分析過程。

重點：
- 體卦是你自己，用卦是你問的事
- 看體用關係就知道結果：用剋體=不利，體生用=要努力，用生體=順利
- 回答要口語化，像朋友聊天一樣，直接說重點就好。
- 不要有前綴的描述或提示詞。

格式：
```markdown
## 摘要
用30字以內總結卦象的狀況

## 你的狀況 (體卦)
直接說這代表什麼

## 事情的狀況 (用卦)
直接說這代表什麼，還有方位提示

## 體用關係
直接說對你有利還是不利

## 現在的情況
直接描述目前狀況

## 變化的關鍵
直接說什麼會改變

## 最終結果
直接說會變成怎樣

## 建議
簡單說該怎麼做
```
""";

    final yijing = Yijing(model.pickerData?.sign6 ?? '');
    final gzDict = GzDate.toDict(model.divineAtGzDate ?? '');

    final layout = yijing.getMeiyiLayout(GanZhi.byName(gzDict["月"]));
    String userPrompt = """
* **問卦性別：** ${model.gender}
* **占卦時間：** ${model.divineAtGzDate}
* **問題：** ${model.question}
* **本卦：** ${layout["八卦"][0].fullname}
* **互卦：** ${layout["八卦"][1].fullname}
* **變卦：** ${layout["八卦"][2].fullname}
## 單卦從體卦開始
""";
    for (var index = 0; index < 5; index++) {
      final gua = layout["單卦"][index];
      userPrompt += "### 卦名：${gua["卦名"]}\n";
      userPrompt += "- 五行：${gua["五行"]}\n";
      if (index == 0) continue;
      userPrompt += "- 生剋：${gua["生剋"]}\n";
      userPrompt += "- 旺衰：${gua["旺衰"]}\n";
      userPrompt += "- 吉凶：${gua["吉凶"]}\n";
      userPrompt += "- 分數：${gua["分數"]}\n";
    }
    return (sysPrompt, userPrompt);
  }
}
