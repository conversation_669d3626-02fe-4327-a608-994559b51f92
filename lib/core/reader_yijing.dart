part of 'reader_base.dart';

class ReaderYijing extends ReaderBase {
  ReaderYijing();

  @override
  String get feature => '''
## 義理解讀（象數哲理）
### 內容：
強調《易經》的哲學義理與宇宙觀，如「天行健，君子以自強不息」、「陰陽互動」、「中庸之道」等。

### 目的：
用於修身養性、理解天地人三才的運作。

### 代表人物：
程頤、朱熹（理學派）。

### 特色：
- 解卦如詩，重在精神層面。
- 非預測工具，而是修養的經典。
''';

  @override
  (String, String) getPrompt(YijingModel model) {
    String sysPrompt = """
你是易經老師，用白話文解讀卦象。直接說結果，不要分析過程。

簡單說明：
- 本卦：現在的狀況
- 變爻：關鍵變化
- 變卦：最終結果
- 互卦：內在因素
- 綜卦：另一個角度
- 錯卦：相反的可能

格式：
```markdown
## 摘要
用30字以內總結卦象的狀況

## 現在的狀況 (本卦) [卦名]
直接說目前是什麼情況

## 關鍵變化 (變爻) [第幾爻]
直接說什麼會改變

## 最終結果 (變卦) [卦名]
直接說會變成怎樣

## 內在因素 (互卦) [卦名]
直接說隱藏的影響

## 另一個角度 (綜卦) [卦名]
從完全相反的角度看事情，揭示潛在的對立面或互補關係，思考如果所有條件都翻轉會是什麼結果。

## 相反可能 (錯卦) [卦名]
從另一個立場或顛倒的視角看問題，揭示事物內部不同面向的關係，思考如果換個角度或換個人來看會發現什麼。

## 實用建議
簡單說該怎麼做
```
""";

    final yijing = Yijing(model.pickerData?.sign6 ?? '');
    String userPrompt = """
* **占卦時間：** ${model.divineAtGzDate}
* **問題：** ${model.question}
* **本卦：** ${Gua64.bySign6(yijing.orginal).fullname}
* **互卦：** ${Gua64.bySign6(yijing.interaction).fullname}
* **變卦：** ${Gua64.bySign6(yijing.changed).fullname}
* **錯卦：** ${Gua64.bySign6(yijing.swap).fullname}
* **綜卦：** ${Gua64.bySign6(yijing.reverse).fullname}
* **變爻：** ${yijing.dongYao}
""";
    return (sysPrompt, userPrompt);
  }
}
