import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:hive_ce_flutter/hive_flutter.dart';
import 'package:wu_core/wu_extensions.dart';
import 'package:wu_core/wu_public.dart';

import '../../core/assets.dart';
import '../../hive/hive_service.dart';
import '../../models/yijing_model.dart';
import '../divine_edit/divine_edit_page.dart';
import '../divine_view/divine_view_page.dart';

class DivineListPage extends StatelessWidget {
  const DivineListPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('占卜紀錄'),
        actions: [
          IconButton(
              icon: const Icon(Icons.add),
              onPressed: () {
                Get.to(() => DivineEditPage());
              }),
        ],
      ),
      body: SafeArea(
        child: ValueListenableBuilder(
          valueListenable: HiveService.I.box.listenable(),
          builder: (context, box, widget) {
            final items = box.values.toList();
            return buildList(items);
          },
        ),
      ),
    );
  }

  Widget buildList(List<YijingModel> items) {
    return Builder(builder: (context) {
      return GroupedListView(
          elements: items,
          sort: true,
          order: GroupedListOrder.DESC,
          groupBy: (YijingModel item) => item.createAt.ymd(),
          groupHeaderBuilder: (YijingModel item) => SolarHeader(
                solar: item.createAt,
                backColor: Theme.of(context).colorScheme.secondary,
                textColor: Theme.of(context).colorScheme.onSecondary,
              ),
          itemBuilder: (context, YijingModel item) {
            return buildItemTile(item);
          });
    });
  }

  Widget buildItemTile(YijingModel item) {
    var subtitle = "${item.divineAtGzDate} - ${item.pickerData?.yijingName}";
    if (item.summary?.isNotEmpty ?? false) subtitle = item.summary!;
    final signList = item.pickerData!.sign6.split('');
    return ListTile(
      title: Text(item.question),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(item.createAt.ymd()),
          Text(subtitle),
          if ((item.summary?.isNotEmpty ?? false) == false)
            Row(children: signList.map((e) => Assets.imgSign(e, width: 30)).toList()),
        ],
      ),
      trailing: IconButton(
        icon: Icon(Icons.delete),
        onPressed: () => deleteItem(item),
      ),
      onTap: () => Get.to(() => DivineViewPage(model: item)),
      onLongPress: () => Get.to(() => DivineEditPage(model: item.clone())),
    );
  }

  void deleteItem(YijingModel item) {
    Get.dialog(AlertDialog(
      title: Text("刪除"),
      content: Text("確定要刪除嗎？"),
      actions: [
        TextButton(
          child: Text("取消"),
          onPressed: () => Get.back(),
        ),
        TextButton(
          child: Text("確定"),
          onPressed: () {
            item.delete();
            Get.back();
          },
        ),
      ],
    ));
  }
}
