import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../models/yijing_model.dart';
import 'date_row.dart';
import 'reading_page.dart';
import 'view_liuyao.dart';
import 'view_tiyong.dart';
import 'view_yijing.dart';

final _viewList = {
  "觀卦": {
    "易經": const ViewYijing(),
    "六爻": const ViewLiuyao(),
    "體用": const ViewTiyong(),
  },
  "解讀": {
    "高島": ReadingPage(mode: ReadingMode.gaodao),
    "體用": ReadingPage(mode: ReadingMode.tiyong),
    "易經": ReadingPage(mode: ReadingMode.yijing),
    "六爻": ReadingPage(mode: ReadingMode.liuyao),
  }
};

class DivineViewPage extends StatefulWidget {
  final YijingModel model;
  const DivineViewPage({super.key, required this.model});

  @override
  State<DivineViewPage> createState() => _DivineViewPageState();
}

class _DivineViewPageState extends State<DivineViewPage> {
  String level1 = '';
  String level2 = '';
  @override
  void initState() {
    level1 = _viewList.keys.first;
    level2 = _viewList[level1]!.keys.first;
    Get.put(widget.model);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('觀卦'),
      ),
      body: SafeArea(
        child: Column(
          spacing: 8,
          children: [
            buildHeader(),
            buildSegment(),
            Expanded(child: _viewList[level1]![level2]!),
          ],
        ),
      ),
    );
  }

  Widget buildSegment() {
    return LayoutBuilder(builder: (_, constraints) {
      final width = constraints.maxWidth * 0.9;
      return Column(
        spacing: 8,
        children: [
          SizedBox(
            width: width,
            child: CupertinoSegmentedControl<String>(
                groupValue: level1,
                children: {for (var e in _viewList.keys) e: Text(e)},
                onValueChanged: (value) {
                  level1 = value;
                  level2 = level1 == "觀卦" ? _viewList[level1]!.keys.first : widget.model.readingMode.name;
                  setState(() {});
                }),
          ),
          SizedBox(
            width: width,
            child: CupertinoSegmentedControl<String>(
                groupValue: level2,
                children: {for (var e in _viewList[level1]!.keys) e: Text(e)},
                onValueChanged: (value) {
                  level2 = value;
                  setState(() {});
                }),
          ),
        ],
      );
    });
  }

  Widget buildHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Column(
        spacing: 8,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DateRow(model: widget.model),
          Text(widget.model.question, style: Get.textTheme.headlineSmall),
        ],
      ),
    );
  }
}
